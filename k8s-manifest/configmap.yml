apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
  namespace: kube-public
  labels:
    k8s-app: fluent-bit
data:
  fluent-bit.conf: |-
    [SERVICE]
        Flush         5
        Log_Level     info
        Daemon        off
        Parsers_File  parsers.conf
        HTTP_Server   On
        HTTP_Listen   0.0.0.0
        HTTP_Port     2020

    @INCLUDE input.conf
    @INCLUDE filter.conf
    @INCLUDE output-kafka.conf

  input.conf: |-
    [INPUT]
        Name              tail
        Tag               tomcat-access
        Path              {{TOMCAT_ACCESS_LOG_PATH}}
        Path_Key          path
        Parser            tomcat_access
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               tomcat-access
        Path              {{TOMCAT_ACCESS_LOG_PATH2}}
        Path_Key          path
        Parser            tomcat_access
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               tomcat-access
        Path              {{TOMCAT_ACCESS_LOG_PATH3}}
        Path_Key          path
        Parser            tomcat_access
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               apibus-log
        Path              {{APIBUS_LOG_PATH}}
        Path_Key          path
        Parser            apibus_log
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               i18n-access-miss
        Path              {{I18N_ACCESS_MISS_PATH}}
        Path_Key          path
        Parser            i18n_access_miss
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               i18n-access-req
        Path              {{I18N_ACCESS_REQ_PATH}}
        Path_Key          path
        Parser            i18n_access_req
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               egress-nginx-access
        Path              {{EGRESS_NGINX_ACCESS_PATH}}
        Parser            egress_nginx_access
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               sentinel-block-log
        Path              {{SENTINEL_BLOCK_LOG_PATH}}
        Path_Key          path
        Parser            sentinel_block_log
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               sentinel-block-log
        Path              {{SENTINEL_BLOCK_LOG_PATH2}}
        Path_Key          path
        Parser            sentinel_block_log
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               sentinel-metrics-log
        Path              {{SENTINEL_METRICS_LOG_PATH}}
        Path_Key          path
        Parser            sentinel_metrics_log
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               sentinel-metrics-log
        Path              {{SENTINEL_METRICS_LOG_PATH2}}
        Path_Key          path
        Parser            sentinel_metrics_log
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              fluentbit_metrics
        Tag               internal_metrics
        scrape_interval   2
    [INPUT]
        Name              tail
        Tag               datapt
        Path              {{DATAPT_LOG_PATH}}
        Path_Key          tag
        Parser            datapt_json
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               imaginary-log
        Path              {{IMAGINARY_LOG_PATH}}
        Parser            {{IMAGINARY_LOG_PARSER}}
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               fs-k8s-app-scaler-metrics
        Path              {{FS_K8S_APP_SCALER_METRICS_PATH}}
        Path_Key          path
        Parser            fs_k8s_app_scaler_metrics
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     20MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               refresh-es
        Path              {{REFRESH_ES_LOG_PATH}}
        Path_Key          path
        Parser            refresh_es
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     20MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               refresh-es
        Path              {{REFRESH_ES_LOG_PATH2}}
        Path_Key          path
        Parser            refresh_es
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     20MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               refresh-es
        Path              {{REFRESH_ES_LOG_PATH3}}
        Path_Key          path
        Parser            refresh_es
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     20MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               data-auth-log
        Path              {{DATA_AUTH_LOG_PATH}}
        Path_Key          path
        Parser            data_auth_log
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     20MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               data-auth-log
        Path              {{DATA_AUTH_LOG_PATH2}}
        Path_Key          path
        Parser            data_auth_log
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     20MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               data-auth-log
        Path              {{DATA_AUTH_LOG_PATH3}}
        Path_Key          path
        Parser            data_auth_log
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     20MB
        Skip_Long_Lines   On
        Refresh_Interval  10

  filter.conf: |-
    [FILTER]
        Name                expect
        Match               tomcat-access
        key_exists          code
        action              warn
    [FILTER]
        Name                parser
        Match               tomcat-access
        Key_Name            path
        Parser              logs_path
        Reserve_Data        true
    [FILTER]
        Name                record_modifier
        Match               tomcat-access
        Remove_key          path
        Record              cluster "{{DEPLOY_K8S_CLUSTER_NAME}}"
    [FILTER]
        Name                lua
        Match               tomcat-access
        script              functions.lua
        call                parse_tomcat_access_time
    [FILTER]
        Name                expect
        Match               apibus-log
        key_exists          status_code
        action              warn
    [FILTER]
        Name                parser
        Match               apibus-log
        Key_Name            path
        Parser              logs_path
        Reserve_Data        true
    [FILTER]
        Name                record_modifier
        Match               apibus-log
        Remove_key          path
        Record              cluster "{{DEPLOY_K8S_CLUSTER_NAME}}"
    [FILTER]
        Name                record_modifier
        Match               egress-nginx-access
        Record              cluster "{{DEPLOY_K8S_CLUSTER_NAME}}"
    [FILTER]
        Name                parser
        Match               sentinel-block-log
        Key_Name            path
        Parser              logs_path
        Reserve_Data        true
    [FILTER]
        Name                record_modifier
        Match               sentinel-block-log
        Remove_key          path
    [FILTER]
        Name                parser
        Match               sentinel-metrics-log
        Key_Name            path
        Parser              logs_path
        Reserve_Data        true
    [FILTER]
        Name                record_modifier
        Match               sentinel-metrics-log
        Remove_key          path
    [FILTER]
        Name                parser
        Match               i18n-access-miss
        Key_Name            path
        Parser              logs_path
        Reserve_Data        true
    [FILTER]
        Name                record_modifier
        Match               i18n-access-miss
        Remove_key          path
    [FILTER]
        Name                parser
        Match               i18n-access-req
        Key_Name            path
        Parser              logs_path
        Reserve_Data        true
    [FILTER]
        Name                record_modifier
        Match               i18n-access-req
        Remove_key          path
    [FILTER]
        Name                lua
        Match               datapt
        script              functions.lua
        call                replace_slash
    [FILTER]
        Name                lua
        Match               datapt
        script              functions.lua
        call                filter_app
    [FILTER]
        Name                record_modifier
        Match               datapt
        Record              cluster "{{DEPLOY_K8S_CLUSTER_NAME}}"
        Record              topic "{{DATAPT_KAFKA_TOPIC}}"
    [FILTER]
        Name                grep
        Match               imaginary-log
        Exclude             path ^\/health
    [FILTER]
        Name                grep
        Match               imaginary-log
        Exclude             log ^.*stderr.*
    [FILTER]
        Name                parser
        Match               rocketmq-client-log
        Key_Name            path
        Parser              logs_path
        Reserve_Data        true
    [FILTER]
        Name                parser
        Match               rocketmq-client-log
        Key_Name            log
        Parser              rocketmq_client_log
        Reserve_Data        true
    [FILTER]
        Name                parser
        Match               fs-k8s-app-scaler-metrics
        Key_Name            path
        Parser              logs_path
        Reserve_Data        true
    [FILTER]
        Name                record_modifier
        Match               fs-k8s-app-scaler-metrics
        Remove_key          path
    [FILTER]
        Name                rewrite_tag
        Match               datapt
        Rule                $datapt_filtered_app ^true$ datapt-filtered true
        Emitter_Name        re_emitted
    [FILTER]
        Name                parser
        Match               refresh-es
        Key_Name            path
        Parser              logs_path
        Reserve_Data        true
    [FILTER]
        Name                record_modifier
        Match               refresh-es
        Remove_key          path
    [FILTER]
        Name                parser
        Match               data-auth-log
        Key_Name            path
        Parser              logs_path
        Reserve_Data        true
    [FILTER]
        Name                record_modifier
        Match               data-auth-log
        Remove_key          path

  output-kafka.conf: |-
    [OUTPUT]
        Name  kafka
        Match tomcat-access
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{TOMCAT_ACCESS_KAFKA_TOPIC}}
        Timestamp_Key timestamp
    [OUTPUT]
        Name  kafka
        Match apibus-log
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{APIBUS_LOG_KAFKA_TOPIC}}
        Timestamp_key timestamp
    [OUTPUT]
        Name  kafka
        Match i18n-access-miss
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{I18N_ACCESS_MISS_KAFKA_TOPIC}}
    [OUTPUT]
        Name  kafka
        Match i18n-access-req
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{I18N_ACCESS_REQ_KAFKA_TOPIC}}
    [OUTPUT]
        Name  kafka
        Match egress-nginx-access
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{EGRESS_NGINX_ACCESS_KAFKA_TOPIC}}
        Timestamp_Key collect_time
    [OUTPUT]
        Name  kafka
        Match sentinel-block-log
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{SENTINEL_BLOCK_LOG_KAFKA_TOPIC}}
    [OUTPUT]
        Name  kafka
        Match sentinel-metrics-log
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{SENTINEL_METRICS_LOG_KAFKA_TOPIC}}
    [OUTPUT]
        name            prometheus_exporter
        match           internal_metrics
        host            0.0.0.0
        port            2021
    [OUTPUT]
        Name  kafka
        Match datapt
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{DATAPT_KAFKA_TOPIC}}
        Timestamp_Key timestamp
    [OUTPUT]
        Name  kafka
        Match imaginary-log
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{IMAGINARY_LOG_KAFKA_TOPIC}}
        Timestamp_Key timestamp
    [OUTPUT]
        Name  kafka
        Match rocketmq-client-log
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{ROCKETMQ_CLIENT_LOG_KAFKA_TOPIC}}
        Timestamp_Key timestamp
    [OUTPUT]
        Name  http
        Match datapt-filtered
        Host {{HTTP_RECEIVER_PROXY_HOST}}
        Port {{HTTP_RECEIVER_PROXY_PORT}}
    [OUTPUT]
        Name  kafka
        Match fs-k8s-app-scaler-metrics
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{FS_K8S_APP_SCALER_METRICS_KAFKA_TOPIC}}
    [OUTPUT]
        Name  kafka
        Match refresh-es
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{REFRESH_ES_LOG_KAFKA_TOPIC}}
    [OUTPUT]
        Name  kafka
        Match data-auth-log
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{DATA_AUTH_LOG_KAFKA_TOPIC}}

  parsers.conf: |-
    [PARSER]
        Name        tomcat_access
        Format      regex
        Regex       ^(?<ent_id>\S*) (?<ent_account>\S*) (?<emp_id>\S*) "(?<x_peer_name>.*)" "(?<x_forwarded_for>.*)" "(?<x_real_ip>.*)" (?<trace_id>\S*) (?<remote_host>\S*) (?<time>\[.*\]) "(?<method>\S*) (?<uri>\S*) (?<protocol>\S*)" (?<code>\d*) (?<bytes_sent>\S*) (?<time_cost>\d*)(?: (?<request_size>\S*)(?: (?<x_fs_parent_span_id>\S*) (?<x_fs_span_id>\S*) (?<x_click_id>\S*))?)?$
        Types code:integer time_cost:integer
    [PARSER]
        Name        logs_path
        Format      regex
        Regex       /.+/.+/(?<profile>.+)/(?<app>.+)/(?<pod>.+)/(?<pod_ip>(?:\d{1,3}\.){3}\d{1,3})
    [PARSER]
        Name        apibus_log
        Format      regex
        Regex       ^(?<time>\S*)\s+\[(?<thread>\S*)\]\s+(?<level>\S*)\s+(?<logger>\S*)\s+(?<tenant_id>\S*)\s+(?<user_id>\S*)\s+(?<caller>\S*)\s+(?<caller_ip>\S*)\s+(?<trace_id>\S*)\s+(?<origin_app_name>\S*)\s+(?<origin_server_ip>\S*)\s+(?<request_method>\S*)\s+(?<request_uri>\S*)\s+(?<status_code>\d+)\s+(?<cost>\d+)$
        Types       status_code:integer cost:integer
    [PARSER]
        Name        i18n_access_miss
        Format      json
    [PARSER]
        Name        i18n_access_req
        Format      json
    [PARSER]
        Name        egress_nginx_access
        Format      regex
        Regex       ^\[(?<access_time>.*)\] \[(?<time_iso8601>.*)\] (?<host>\S*) (?<remote_addr>\S*) \[(?<method>\S*) (?<request_url>\S*) (?<http_version>\S*)\] (?<status>\d{3}) (?<request_time>\d+\.\d+) (?<request_length>\d+) (?<bytes_sent>\d+) \[(?<user_agent>.*)\] (?<connect_host>\S*) (?<connect_addr>\S*) (?<proxy_connect_time>.*)$
        Types       status:integer request_time:float request_length:integer bytes_sent:integer access_time:float
    [PARSER]
        Name        sentinel_block_log
        Format      regex
        Regex       ^(?<time_log>.*)\|(?<trace_id>\S*)\|(?<user_id>\S*)\|(?<count>\d+)\|(?<resource_name>\S*)\|(?<interception_reason>\S*)\|(?<rule>\S*)\|(?<origin>\S*)\|(?<rule_id>\d+)\|(?<blocked_request>\d+)\|(?<curr_thread_num>\d+)\|(?<args>.*)$
        Types       sequence_in_second:integer interception_count:integer
    [PARSER]
        Name        sentinel_metrics_log
        Format      regex
        Regex       ^(?<time_log>.*)\|(?<trace_id>\S*)\|(?<user_id>\S*)\|(?<timestamp_>\d+)\|(?<time>.*)\|(?<resource_name>.*)\|(?<passed_qps>\d+)\|(?<blocked_qps>\d+)\|(?<success_qps>\d+)\|(?<exception_qps>\d+)\|(?<rt>\d+)\|(?<occupied_pass_qps>\d+)\|(?<concurrency>\d+)\|(?<classification>\d+)$
        Types       timestamp:integer passed_qps:integer blocked_qps:integer exit_qps:integer exception_count:integer response_time_avg:integer future_taken:integer max_concurrency:integer resource_category:integer
    [PARSER]
        Name        datapt_json
        Format      json
        Time_Key    time
        Time_Format %Y-%m-%dT%H:%M:%S %z
    [PARSER]
        Name        imaginary_log
        Format      regex
        Regex       ^{"log":"(?<ip>[^ ]*)\s+[^ ]*\s+[^ ]*\s+\[(?<time>[^\]]*)\]\s+\\"(?<method>\S+)\s+(?<path>[^ ]*)\s+(?<protocol>[^ ]*)\\"\s+(?<code>[^ ]*)\s+(?<size>[^ ]*)\s+(?<response_time>[^ ]*)\\n
        Types       status:integer 
    [PARSER]
        Name        imaginary_log_firstshare
        Format      regex
        Regex       ^[^ ]*\s+[^ ]*\s+[^ ]*\s+(?<ip>[^ ]*)\s+[^ ]*\s+[^ ]*\s+\[(?<time>[^\]]*)\]\s+"(?<method>\S+)\s+(?<path>[^ ]*)\s+(?<protocol>[^ ]*)"\s+(?<code>[^ ]*)\s+(?<size>[^ ]*)\s+(?<response_time>[^ ]*)
        Types       status:integer 
    [PARSER]
        Name        rocketmq_client_log
        Format      regex
        Regex       ^(?<time>[0-9]{4}-[0-9]{2}-[0-9]{2}\s[0-9]{2}:[0-9]{2}:[0-9]{2})([,\.][0-9]{3})?\s+(?<level>[^ ]*)\s+(?<category>[^ ]*)\s+(?<thread>[^ ]*)\s+(?<message>.{0,256})[^\n]*(?<stack>(\n^(?!\d{4}-\d{2}-\d{2}).*)*)$
    [PARSER]
        Name        fs_k8s_app_scaler_metrics
        Format      json    
    [PARSER]
        Name        refresh_es
        Format      json
    [PARSER]
        Name        data_auth_log
        Format      json
        
    [MULTILINE_PARSER]
        name          rocketmq_multiline_parser
        type          regex
        flush_timeout 1000
        key_content   log
        #
        # Regex rules for multiline parsing
        # ---------------------------------
        #
        # configuration hints:
        #
        #  - first state always has the name: start_state
        #  - every field in the rule must be inside double quotes
        #
        # rules |   state name  | regex pattern                  | next state
        # ------|---------------|--------------------------------------------
        rule      "start_state"   "/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})(.*)/"  "cont"
        rule      "cont"          "/(org.*)|(See.*)|(\s+at.*)/"                     "cont"
    
  functions.lua: |-
    function replace_slash(tag, timestamp, record)
        if record["tag"] then
            record["tag"] = string.gsub(record["tag"], "/", ".")
        end
        return 2, timestamp, record
    end
    
    function filter_app(tag, timestamp, record)
        local app_name = record["$app_name"]
        local filter_set = "{{DATAPT_FILTER_APP_NAME}}"
        if filter_set == "-" then
            return 1, timestamp, record
        end
        if app_name and string.find("|" .. filter_set .. "|", "|" .. app_name .. "|", 1, true) then
            record["datapt_filtered_app"] = "true"
        end
        if filter_set == "*" then
            record["datapt_filtered_app"] = "true"
        end
        return 1, timestamp, record
    end
    
    -- 兼容带有毫秒和没有毫秒的时间格式解析
    function parse_tomcat_access_time(tag, timestamp, record)
        local months = { Jan = 1, Feb = 2, Mar = 3, Apr = 4, May = 5, Jun = 6,
                         Jul = 7, Aug = 8, Sep = 9, Oct = 10, Nov = 11, Dec = 12 }
        local time = record["time"]
        local day, mon, year, hour, min, sec, msec, sign, h, m = time:match("(%d+)/(%a+)/(%d+):(%d+):(%d+):(%d+)(%.?%d?%d?%d?) ([+-])(%d%d)(%d%d)")
        --print(day, mon, year, hour, min, sec, msec, sign, h, m )
        
        local t = {
            year = tonumber(year),
            month = months[mon],
            day = tonumber(day),
            hour = tonumber(hour),
            min = tonumber(min),
            sec = tonumber(sec)
        }
        
        local timestamp_ = os.time(t)
        
        local offset = tonumber(h) * 3600 + tonumber(m) * 60
        if sign == "-" then
            offset = -offset
        end
        
        -- 修正时区
        timestamp_ = timestamp_ - os.difftime(os.time(os.date("*t", timestamp_)), timestamp_) + offset
        
        local ms = 0
        if msec and #msec > 1 then
            ms = tonumber(msec:sub(2)) / 1000
        end
        
        record["time"] = timestamp_ + ms
        return 2, timestamp, record
    end
