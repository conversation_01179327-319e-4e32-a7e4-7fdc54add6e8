apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluent-bit
  namespace: kube-public
  labels:
    app: fluent-bit
    version: v1
spec:
  selector:
    matchLabels:
      app: fluent-bit
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 30
  template:
    metadata:
      annotations:
        prometheus.io/path: /api/v1/metrics/prometheus
        prometheus.io/port: "2020"
        prometheus.io/scrape: "true"
        fxiaoke.com/managed-by: gitlab-ci
        fxiaoke.com/commit-sha: "{{CI_COMMIT_SHORT_SHA}}"
      labels:
        app: fluent-bit
        version: v1
    spec:
      hostPID: true
      hostIPC: true
      containers:
        - env:
            - name: TZ
              value: Asia/Shanghai
          image: "{{DEPLOY_APP_IMAGE}}"
          name: fluent-bit
          ports:
            - containerPort: 2020
              protocol: TCP
          securityContext:
            privileged: true
          stdin: true
          stdinOnce: true
          tty: true
          resources:
            requests:
              cpu: 50m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 512Mi
          volumeMounts:
            - mountPath: /var/log
              name: varlog
            - mountPath: /data
              name: host-data
              readOnly: true
            - mountPath: /fluent-bit/etc/
              name: fluent-bit-config
      tolerations:
        - operator: Exists
      volumes:
        - hostPath:
            path: /var/log
            type: ""
          name: varlog
        - hostPath:
            path: "{{HOST_DATA_PATH}}"
          name: host-data
        - configMap:
            defaultMode: 420
            name: fluent-bit-config
          name: fluent-bit-config