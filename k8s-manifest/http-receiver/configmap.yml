apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config-http-receiver
  namespace: monitoring
  labels:
    k8s-app: fluent-bit
data:
  fluent-bit.conf: |-
    [SERVICE]
        Flush         5
        Log_Level     info
        Daemon        off

    @INCLUDE input.conf
    @INCLUDE output.conf

  input.conf: |-
    [INPUT]
        name                http
        tag_key             topic
        listen              0.0.0.0
        port                9880

  output.conf: |-
    [OUTPUT]
        Name  kafka
        Match {{DATAPT_KAFKA_TOPIC}}
        Brokers {{KAFKA_BROKERS_DEFAULT}}
        Topics  {{DATAPT_KAFKA_TOPIC}}
        Timestamp_Key timestamp