apiVersion: apps/v1
kind: Deployment
metadata:
  name: fluent-bit-http-receiver
  namespace: monitoring
  labels:
    k8s-app: fluent-bit
    app: fluent-bit-http-receiver
spec:
  replicas: 1
  selector:
    matchLabels:
      app: fluent-bit-http-receiver
  template:
    metadata:
      name: fluent-bit-http-receiver
      namespace: monitoring
      labels:
        app: fluent-bit-http-receiver
    spec:
      serviceAccountName: {{K8S_EVENTS_SERVICE_ACCOUNT}}
      containers:
        - image: reg.foneshare.cn/docker.io/fluent/fluent-bit:2.1.10-debug
          imagePullPolicy: IfNotPresent
          name: fluent-bit
          ports:
            - containerPort: 2020
              protocol: TCP
            - containerPort: 9880
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 500m
              memory: 512Mi
          securityContext:
            privileged: true
          volumeMounts:
            - mountPath: /fluent-bit/etc/
              name: fluent-bit-config
      volumes:
        - configMap:
            defaultMode: 420
            name: fluent-bit-config-http-receiver
          name: fluent-bit-config
