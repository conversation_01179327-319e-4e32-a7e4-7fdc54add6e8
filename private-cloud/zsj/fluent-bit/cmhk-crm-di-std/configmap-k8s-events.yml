apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config-k8s-events
  namespace: cmhk-crm-di-std
data:
  fluent-bit.conf: |-
    [SERVICE]
        Flush         5
        Log_Level     info
        Daemon        off

    @INCLUDE input.conf
    @INCLUDE filter.conf
    @INCLUDE output.conf

  input.conf: |-
    [INPUT]
        Name                kubernetes_events
        Tag                 kubernetes_events
        interval_sec        5
        kube_namespace      cmhk-crm-di-std
        kube_url            https://kubernetes.default.svc
        Kube_CA_File        /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File     /var/run/secrets/kubernetes.io/serviceaccount/token

  filter.conf: |-
    [FILTER]
        Name          lua
        Match         kubernetes_events
        script        functions.lua
        call          k8s_events

  output.conf: |-
    [OUTPUT]
        Name          stdout
        Match         kubernetes_events
    [OUTPUT]
        Name  kafka
        Match kubernetes_events
        Brokers *************:4308,*************:4308,*************:4308
        Topics  k8s-events

  functions.lua: |-
    function k8s_events(tag, timestamp, record)
        record['metadata_name'] = record['metadata']['name']
        record['metadata_namespace'] = record['metadata']['namespace']
        record['metadata_uid'] = record['metadata']['uid']
        record['metadata_creationTimestamp'] = record['metadata']['creationTimestamp']
        record['metadata'] = nil
        record['involvedObject_kind'] = record['involvedObject']['kind']
        record['involvedObject_namespace'] = record['involvedObject']['namespace']
        record['involvedObject_name'] = record['involvedObject']['name']
        record['involvedObject'] = nil
        record['source_component'] = record['source']['component']
        record['source_host'] = record['source']['host']
        record['source'] = nil
        return 2, timestamp, record
    end
