apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluent-bit
  namespace: cmhk-crm-di-std
  labels:
    app: fluent-bit
    version: v1
spec:
  selector:
    matchLabels:
      app: fluent-bit
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 10
  template:
    metadata:
      annotations:
        prometheus.io/path: /api/v1/metrics/prometheus
        prometheus.io/port: "2020"
        prometheus.io/scrape: "true"
      labels:
        app: fluent-bit
        version: v1
    spec:
      containers:
        - env:
            - name: TZ
              value: Asia/Shanghai
          image: "registry-c.cmft.com/cmhk-crm-di-proxytofxharbo/docker.io/fluent/fluent-bit:1.9.3"
          name: fluent-bit
          ports:
            - containerPort: 2020
              protocol: TCP
          securityContext:
            privileged: true
          resources:
            requests:
              cpu: 50m
              memory: 128Mi
            limits:
              cpu: 50m
              memory: 128Mi
          volumeMounts:
            - mountPath: /var/log
              name: varlog
            - mountPath: /host-data
              name: host-data
              readOnly: true
            - mountPath: /fluent-bit/etc/
              name: fluent-bit-config
      volumes:
        - hostPath:
            path: /var/log
            type: ""
          name: varlog
        - hostPath:
            path: /var/lib/
            type: ""
          name: host-data
        - configMap:
            defaultMode: 420
            name: fluent-bit-config
          name: fluent-bit-config