apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
  namespace: cmhk-crm-prd-std
  labels:
    k8s-app: fluent-bit
data:
  fluent-bit.conf: |-
    [SERVICE]
        Flush         5
        Log_Level     info
        Daemon        off
        Parsers_File  parsers.conf
        HTTP_Server   On
        HTTP_Listen   0.0.0.0
        HTTP_Port     2020

    @INCLUDE input.conf
    @INCLUDE filter.conf
    @INCLUDE output-kafka.conf

  input.conf: |-
    [INPUT]
        Name              tail
        Tag               tomcat-access
        Path              /host-data/kubelet/pods/*/volumes/kubernetes.io~empty-dir/logs/.links/*/*/*/*/logs/tomcat-access.*
        Path_Key          path
        Parser            tomcat_access
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     5MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               tomcat-access
        Path              /host-data/kubelet/pods/*/volumes/kubernetes.io~empty-dir/logs/log-links/*/*/*/*/logs/tomcat-access.*
        Path_Key          path
        Parser            tomcat_access
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     5MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               tomcat-access
        Path              /host-data/kubelet/pods/*/volumes/kubernetes.io~empty-dir/logs/_links/*/*/*/*/logs/tomcat-access.*
        Path_Key          path
        Parser            tomcat_access
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     5MB
        Skip_Long_Lines   On
        Refresh_Interval  10
    [INPUT]
        Name              tail
        Tag               datapt
        Path              /host-data/containers/storage/overlay/*/merged/opt/applogs/datapt/*/*.log,/host-data/kubelet/pods/*/volumes/kubernetes.io~empty-dir/applogs/datapt/*/*.log
        Path_Key          path
        Parser            datapt_json
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     5MB
        Skip_Long_Lines   On
        Refresh_Interval  10

  filter.conf: |-
    [FILTER]
      Name                expect
      Match               tomcat-access
      key_exists          code
      action              warn
    [FILTER]
      Name                parser
      Match               tomcat-access
      Key_Name            path
      Parser              tomcat_access_file_path
      Reserve_Data        true
    [FILTER]
        Name          record_modifier
        Match         tomcat-access
        Remove_key    path 
    [FILTER]
        Name          lua
        Match         datapt
        script        functions.lua
        call          replace_slash

  output-kafka.conf: |-
    [OUTPUT]
        Name  kafka
        Match tomcat-access
        Brokers ************:9092,*************:9092,*************:9092
        Topics  tomcat-access
        Timestamp_Key timestamp
    [OUTPUT]
        Name  kafka
        Match datapt
        Brokers ************:9092,*************:9092,*************:9092
        Topics  k8s-app-datapt
        Timestamp_Key timestamp

  parsers.conf: |-
    [PARSER]
        Name        tomcat_access
        Format      regex
        Regex       ^(?<ent_id>\S*) (?<ent_account>\S*) (?<emp_id>\S*) "(?<x_peer_name>.*)" "(?<x_forwarded_for>.*)" "(?<x_real_ip>.*)" (?<trace_id>\S*) (?<remote_host>\S*) (?<time>\[.*\]) "(?<method>\S*) (?<uri>\S*) (?<protocol>\S*)" (?<code>\d*) (?<bytes_sent>\S*) (?<time_cost>\d*) ?(?<request_size>\S*)?$
        Time_Key    time
        Time_format [%d/%b/%Y:%H:%M:%S %z]
        Types code:integer time_cost:integer
    [PARSER]
        Name        tomcat_access_file_path
        Format      regex
        Regex       /.+/logs/.+/(?<profile>.+)/(?<app>.+)/(?<pod>.+)/(?<pod_ip>.+)/logs
    [PARSER]
        Name        datapt_json
        Format      json
        Time_Key    time
        Time_Format %Y-%m-%dT%H:%M:%S %z

  functions.lua: |-
    function replace_slash(tag, timestamp, record)
        if record["path"] then
            record["path"] = string.gsub(record["path"], "/", ".")
        end
        return 2, timestamp, record
    end