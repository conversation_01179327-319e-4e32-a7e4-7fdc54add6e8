apiVersion: apps/v1
kind: Deployment
metadata:
  name: fluent-bit-k8s-events
  namespace: sinotrans-gf-sino-zscrm-prd
  labels:
    app: fluent-bit-k8s-events
    version: v2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: fluent-bit-k8s-events
  template:
    metadata:
      name: fluent-bit-k8s-events
      namespace: kube-public
      labels:
        app: fluent-bit-k8s-events
    spec:
      containers:
        - image: registry-d.cmft.com/sinotrans-gf-sino-zscrm-proxy-crm-di/cmhk-crm-di-proxytofxharbo/docker.io/fluent/fluent-bit:2.1.10
          imagePullPolicy: IfNotPresent
          name: fluent-bit
          ports:
            - containerPort: 2020
              protocol: TCP
          resources:
            limits:
              cpu: 50m
              memory: 512Mi
            requests:
              cpu: 50m
              memory: 128Mi
          securityContext:
            privileged: true
          volumeMounts:
            - mountPath: /fluent-bit/etc/
              name: fluent-bit-config
      volumes:
        - configMap:
            defaultMode: 420
            name: fluent-bit-config-k8s-events
          name: fluent-bit-config
