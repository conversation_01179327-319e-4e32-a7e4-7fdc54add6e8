[SERVICE]
      Flush         5
      Log_Level     info
      HTTP_Server   On
      HTTP_Listen   0.0.0.0
      HTTP_Port     2020
[INPUT]
      Name              tail
      Tag               nginx-access
      Path              /var/log/nginx/nginx-access.* # 需要采集的文件
      Path_Key          path
      DB                /var/log/flb_kube.db # db文件目录
      Mem_Buf_Limit     5MB
      Skip_Long_Lines   On
      Refresh_Interval  10
[INPUT]
      Name              tail
      Tag               nginx-access-fgif
      Path              /var/log/nginx/nginx-access.* # 需要采集的文件
      Path_Key          path
      DB                /var/log/flb_kube.db # db文件目录
      Mem_Buf_Limit     5MB
      Skip_Long_Lines   On
      Refresh_Interval  10
[INPUT]
      Name              tail
      Tag               nginx-access-mgif
      Path              /var/log/nginx/nginx-access.* # 需要采集的文件
      Path_Key          path
      DB                /var/log/flb_kube.db # db文件目录
      Mem_Buf_Limit     5MB
      Skip_Long_Lines   On
      Refresh_Interval  10
[INPUT]
      Name              tail
      Tag               nginx-access-pgif
      Path              /var/log/nginx/nginx-access.* # 需要采集的文件
      Path_Key          path
      DB                /var/log/flb_kube.db # db文件目录
      Mem_Buf_Limit     5MB
      Skip_Long_Lines   On
      Refresh_Interval  10
[FILTER]
      Name          record_modifier
      Match         nginx-access
      Remove_key    path
      Record        host_ip "xxx.xxx.xxx.xxx" # 追加字段
[FILTER]
      Name          record_modifier
      Match         nginx-access-fgif
      Remove_key    path
      Record        host_ip "xxx.xxx.xxx.xxx" # 追加字段
[FILTER]
      Name          record_modifier
      Match         nginx-access-mgif
      Remove_key    path
      Record        host_ip "xxx.xxx.xxx.xxx" # 追加字段
[FILTER]
      Name          record_modifier
      Match         nginx-access-pgif
      Remove_key    path
      Record        host_ip "xxx.xxx.xxx.xxx" # 追加字段
[OUTPUT]
      Name  kafka
      Match nginx-access
      Brokers ***********:9092,***********:9092 # kafka brokers
      Topics  nginx-access # kafka topic
      Timestamp_Key timestamp
[OUTPUT]
      Name  kafka
      Match nginx-access-fgif
      Brokers ***********:9092,***********:9092 # kafka brokers
      Topics  nginx-access-fgif # kafka topic
      Timestamp_Key timestamp
[OUTPUT]
      Name  kafka
      Match nginx-access-mgif
      Brokers ***********:9092,***********:9092 # kafka brokers
      Topics  nginx-access-mgif # kafka topic
      Timestamp_Key timestamp
[OUTPUT]
      Name  kafka
      Match nginx-access-pgif
      Brokers ***********:9092,***********:9092 # kafka brokers
      Topics  nginx-access-pgif # kafka topic
      Timestamp_Key timestamp
[OUTPUT]
      Name  stdout
      Match nginx-access