[PARSER]
    Name        pg_error
    Format      regex
    Regex       ^(?<time>\S*\s+\S*)\s+\S*\s+\[(?<process_id>\d+)\]:\s+\[(?<session_id>\S*)\]--\s+\[(?<session_line_number>\d+)-1\]\s+(?<transaction_id>\d+)\s+(?<virtual_transaction_id>\S*)\s+user=(?<user_name>\S*),db=(?<database_name>\S*),app=\[(?<app_name>\S*)\],client=(?<remote_host>\S*)(?<level>ERROR|WARNING|FATAL|PANIC|DEADLOCK|stale statistics):\s+(?<message>.*)
    Types       process_id:integer session_line_number:integer transaction_id:integer
